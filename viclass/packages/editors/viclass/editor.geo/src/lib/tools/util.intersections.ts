import { Line, Point, point, vector } from '@flatten-js/core';
import { cos, sin, sqrt } from '../extension.function/number.ext.func';
import { RenderCircle, RenderEllipse, RenderLine, RenderSector, StrokeType } from '../model';
import { GeoDocCtrl } from '../objects';
import { Coefficients } from '../solving/coefficients';
import { Complex } from '../solving/complex';
import { PolyBase } from '../solving/poly.solving';
import { createExtractFlattenLine, createFlattenCircle, createFlattenLine } from './util.flatten';
import { isElementLine } from './util.tool';
import { isLargerThan, pointsByRotation, sortOnLineV2 } from '../nth.direction';

// =================== TYPES & UTILITIES ===================

export function isPointInSector(pt: Point, sector: RenderSector, docCtrl: GeoDocCtrl): boolean {
    try {
        const centerCoords = sector.coord('center', docCtrl.rendererCtrl);
        const center = point(centerCoords[0], centerCoords[1]);

        const dx = pt.x - center.x;
        const dy = pt.y - center.y;
        let pointAngle = Math.atan2(dy, dx);
        if (pointAngle < 0) pointAngle += 2 * Math.PI;

        const startCoords = sector.coord('start', docCtrl.rendererCtrl);
        const endCoords = sector.coord('end', docCtrl.rendererCtrl);
        if (!startCoords || !endCoords) return true;

        const startDx = startCoords[0] - center.x;
        const startDy = startCoords[1] - center.y;
        let startAngle = Math.atan2(startDy, startDx);
        if (startAngle < 0) startAngle += 2 * Math.PI;

        const endDx = endCoords[0] - center.x;
        const endDy = endCoords[1] - center.y;
        let endAngle = Math.atan2(endDy, endDx);
        if (endAngle < 0) endAngle += 2 * Math.PI;

        return startAngle <= endAngle
            ? pointAngle >= startAngle && pointAngle <= endAngle
            : pointAngle >= startAngle || pointAngle <= endAngle;
    } catch {
        return false;
    }
}

export function createCircleFromSector(sector: RenderSector): RenderCircle {
    const circle = new RenderCircle();

    circle.centerPointIdx = sector.centerPointIdx;
    circle.radius = sector.radius;
    circle.length = 2 * Math.PI * sector.radius;

    return circle;
}

/**
 * Checks if a point is on a specific element using the appropriate isPointOn* function
 */
export function isPointOnElement(point: Point, element: StrokeType, docCtrl: GeoDocCtrl): boolean {
    try {
        switch (element.type) {
            case 'RenderLineSegment':
            case 'RenderRay':
            case 'RenderVector':
                return createExtractFlattenLine(element as RenderLine, docCtrl).intersect(point).length > 0;
            case 'RenderSector':
                return isPointInSector(point, element as RenderSector, docCtrl);
            default:
                return true;
        }
    } catch (error) {
        console.warn('Error checking if point is on element:', error);
        return false;
    }
}

/**
 * Filters intersection points to only include those within element bounds
 * Uses isPointOn* functions to check if intersections are on the actual elements
 */
export function filterVisibleIntersections(
    intersections: Point[],
    element1: StrokeType,
    element2: StrokeType,
    docCtrl: GeoDocCtrl
): Point[] {
    if (!intersections || intersections.length === 0) return [];
    return intersections.filter(intersection => {
        const point = new Point(intersection.x, intersection.y);
        return isPointOnElement(point, element1, docCtrl) && isPointOnElement(point, element2, docCtrl);
    });
}

/**
 * Calculates intersection points between two elements
 * @param includeOutside - Whether to include intersections outside element bounds
 */
export function calculateIntersections(
    element1: StrokeType,
    element2: StrokeType,
    docCtrl: GeoDocCtrl
): Point[] | null {
    try {
        // Line-Line intersection
        if (isElementLine(element1) && isElementLine(element2))
            return calculateLineLineIntersection(element1 as RenderLine, element2 as RenderLine, docCtrl);

        // Line-Circle intersection
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        ) {
            const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
            const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
            return calculateLineCircleIntersection(line, circle, docCtrl);
        }

        // Line-Ellipse intersection
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        ) {
            const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
            const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
            return intersectionLineEllipse(line, ellipse, docCtrl);
        }

        // Circle-Circle intersection
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') {
            if (!isLargerThan(element1.relIndex, element2.relIndex))
                return calculateCircleCircleIntersection(element1 as RenderCircle, element2 as RenderCircle, docCtrl);
            else return calculateCircleCircleIntersection(element2 as RenderCircle, element1 as RenderCircle, docCtrl);
        }

        // Circle-Ellipse intersection
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        ) {
            const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
            const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
            return intersectionCircleEllipse(circle, ellipse, docCtrl);
        }

        // Ellipse-Ellipse intersection
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') {
            if (!isLargerThan(element1.relIndex, element2.relIndex))
                return intersectionEllipses(element1 as RenderEllipse, element2 as RenderEllipse, docCtrl);
            else return intersectionEllipses(element2 as RenderEllipse, element1 as RenderEllipse, docCtrl);
        }

        // Line-Sector intersection
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        ) {
            const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
            const sector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
            return calculateLineCircleIntersection(line, createCircleFromSector(sector), docCtrl);
        }

        // Circle-Sector intersection
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        ) {
            if (element1.type === 'RenderCircle') {
                return calculateCircleCircleIntersection(
                    element1 as RenderCircle,
                    createCircleFromSector(element2 as RenderSector),
                    docCtrl
                );
            } else {
                return calculateCircleCircleIntersection(
                    element2 as RenderCircle,
                    createCircleFromSector(element1 as RenderSector),
                    docCtrl
                );
            }
        }

        // Ellipse-Sector intersection
        if (
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
        ) {
            if (element1.type === 'RenderEllipse') {
                return intersectionCircleEllipse(
                    createCircleFromSector(element2 as RenderSector),
                    element1 as RenderEllipse,
                    docCtrl
                );
            } else {
                return intersectionCircleEllipse(
                    createCircleFromSector(element1 as RenderSector),
                    element2 as RenderEllipse,
                    docCtrl
                );
            }
        }

        // Sector-Sector intersection
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') {
            if (!isLargerThan(element1.relIndex, element2.relIndex))
                return calculateCircleCircleIntersection(
                    createCircleFromSector(element1 as RenderSector),
                    createCircleFromSector(element2 as RenderSector),
                    docCtrl
                );
            else
                return calculateCircleCircleIntersection(
                    createCircleFromSector(element2 as RenderSector),
                    createCircleFromSector(element1 as RenderSector),
                    docCtrl
                );
        }

        return null;
    } catch (error) {
        console.warn('Error calculating intersections:', error);
        return null;
    }
}

export function getCenterPoint(element: StrokeType, docCtrl: GeoDocCtrl): Point | undefined {
    const centerCoords = (element as RenderCircle | RenderSector | RenderEllipse).coord('center', docCtrl.rendererCtrl);
    return point(centerCoords[0], centerCoords[1]);
}

/**
 * Orders intersections using the same logic as the backend based on intersection type
 */
export function getOrderedIntersections(
    stroke1: StrokeType,
    stroke2: StrokeType,
    allIntersections: Point[],
    docCtrl: GeoDocCtrl
): Point[] | undefined {
    if (allIntersections.length === 0) return undefined;

    // Convert intersection points to Point objects
    const intersectionPoints = allIntersections.map(pt => point(pt.x, pt.y));

    // Apply the same ordering logic as the backend
    if (isElementLine(stroke1) && isElementLine(stroke2)) {
        return intersectionPoints;
    } else if (
        (isElementLine(stroke1) && (stroke2.type === 'RenderCircle' || stroke2.type === 'RenderSector')) ||
        ((stroke1.type === 'RenderCircle' || stroke1.type === 'RenderSector') && isElementLine(stroke2)) ||
        (isElementLine(stroke1) && stroke2.type === 'RenderEllipse') ||
        (stroke1.type === 'RenderEllipse' && isElementLine(stroke2))
    ) {
        // Line-Circle, Line-Sector, Line-Ellipse: order by parallel vector
        const line = (isElementLine(stroke1) ? stroke1 : stroke2) as RenderLine;
        const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
        const directionVector = vector(orderedVector[0], orderedVector[1]);
        return sortOnLineV2(directionVector, intersectionPoints);
    } else if (
        (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderCircle') ||
        (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderSector') ||
        (stroke1.type === 'RenderSector' && stroke2.type === 'RenderCircle') ||
        (stroke1.type === 'RenderSector' && stroke2.type === 'RenderSector') ||
        (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderEllipse') ||
        (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderEllipse') ||
        (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderCircle') ||
        (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderSector') ||
        (stroke1.type === 'RenderSector' && stroke2.type === 'RenderEllipse')
    ) {
        const first = isLargerThan(stroke1.relIndex, stroke2.relIndex) ? stroke2 : stroke1;
        const second = first === stroke1 ? stroke2 : stroke1;

        // Circle-Circle, Ellipse-Ellipse, Circle-Ellipse: order by rotation
        const center1 = getCenterPoint(first, docCtrl);
        const center2 = getCenterPoint(second, docCtrl);
        if (!center1 || !center2) return intersectionPoints;

        const connectionVector = point(center2.x - center1.x, center2.y - center1.y);
        return pointsByRotation(connectionVector, center1, intersectionPoints);
    }

    return intersectionPoints;
}

// =================== INTERSECTION FUNCTIONS ===================

export function calculateLineLineIntersection(line1: RenderLine, line2: RenderLine, docCtrl: GeoDocCtrl) {
    const flattenLine1 = createFlattenLine(line1, docCtrl);
    const flattenLine2 = createFlattenLine(line2, docCtrl);
    return flattenLine1.intersect(flattenLine2);
}

export function calculateLineCircleIntersection(line: RenderLine, circle: RenderCircle, docCtrl: GeoDocCtrl) {
    const flattenLine = createFlattenLine(line, docCtrl);
    const flattenCircle = createFlattenCircle(circle, docCtrl);
    return flattenLine.intersect(flattenCircle);
}

export function calculateCircleCircleIntersection(circle1: RenderCircle, circle2: RenderCircle, docCtrl: GeoDocCtrl) {
    const flattenCircle1 = createFlattenCircle(circle1, docCtrl);
    const flattenCircle2 = createFlattenCircle(circle2, docCtrl);
    return flattenCircle1.intersect(flattenCircle2);
}

export function intersectionLineEllipse(line: RenderLine, ellipse: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const flattenLine = createFlattenLine(line, docCtrl);

    const centerCoords = ellipse.coord('center', docCtrl.rendererCtrl);
    const pC = point(centerCoords[0], centerCoords[1]);

    return intersectionLineEllipseLegacy(flattenLine, pC, ellipse.a, ellipse.b, ellipse.rotate);
}

export function intersectionCircleEllipse(circle: RenderCircle, ellipse: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const centerCoords = circle.coord('center', docCtrl.rendererCtrl);
    const cC = point(centerCoords[0], centerCoords[1]);
    const cR = circle.radius;

    const ellipseCenterCoords = ellipse.coord('center', docCtrl.rendererCtrl);
    const pC = point(ellipseCenterCoords[0], ellipseCenterCoords[1]);

    return intersectionCircleEllipseLegacy(cC, cR, pC, ellipse.a, ellipse.b, ellipse.rotate);
}

export function intersectionEllipses(ellipse1: RenderEllipse, ellipse2: RenderEllipse, docCtrl: GeoDocCtrl): Point[] {
    const center1Coords = ellipse1.coord('center', docCtrl.rendererCtrl);
    const pC1 = point(center1Coords[0], center1Coords[1]);

    const center2Coords = ellipse2.coord('center', docCtrl.rendererCtrl);
    const pC2 = point(center2Coords[0], center2Coords[1]);

    return intersectionEllipsesLegacy(
        pC1,
        ellipse1.a,
        ellipse1.b,
        ellipse1.rotate,
        pC2,
        ellipse2.a,
        ellipse2.b,
        ellipse2.rotate
    );
}

// =================== LEGACY FUNCTIONS ===================

export function intersectionLineEllipseLegacy(
    line: Line,
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number
): Point[] {
    const h = pC.x;
    const k = pC.y;
    const A = rotateRadEll;

    const a = cos(A).pow(2) / aEll.pow(2) + sin(A).pow(2) / bEll.pow(2);
    const b = sin(A).pow(2) / aEll.pow(2) + cos(A).pow(2) / bEll.pow(2);
    const c = sin(2 * A) / aEll.pow(2) - sin(2 * A) / bEll.pow(2);
    const d =
        (-2 * h * cos(A).pow(2)) / aEll.pow(2) -
        (k * sin(2 * A)) / aEll.pow(2) -
        (2 * h * sin(A).pow(2)) / bEll.pow(2) +
        (k * sin(2 * A)) / bEll.pow(2);
    const e =
        (-h * sin(2 * A)) / aEll.pow(2) -
        (2 * k * sin(A).pow(2)) / aEll.pow(2) +
        (h * sin(2 * A)) / bEll.pow(2) -
        (2 * k * cos(A).pow(2)) / bEll.pow(2);
    const f =
        (h.pow(2) * cos(A).pow(2)) / aEll.pow(2) +
        (h * k * sin(2 * A)) / aEll.pow(2) +
        (k.pow(2) * sin(A).pow(2)) / aEll.pow(2) +
        (h.pow(2) * sin(A).pow(2)) / bEll.pow(2) -
        (h * k * sin(2 * A)) / bEll.pow(2) +
        (k.pow(2) * cos(A).pow(2)) / bEll.pow(2) -
        1;

    const n = line.norm;
    const u = n.x;
    const v = n.y;
    const m = u * line.pt.x + v * line.pt.y;

    const can = sqrt(
        e.pow(2) * u.pow(2) -
            4 * b * u.pow(2) * f +
            2 * e * c * m * u +
            4 * c * u * v * f -
            2 * e * d * u * v -
            4 * b * m * d * u +
            c.pow(2) * m.pow(2) +
            2 * c * m * d * v +
            d.pow(2) * v.pow(2) -
            4 * b * a * m.pow(2) -
            4 * a * v.pow(2) * f -
            4 * e * a * m * v
    );
    const x1 =
        (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u + v * can) /
        (2 * (-b * u.pow(2) + c * u * v - a * v.pow(2)));
    const y1 =
        (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v - u * can) /
        (2 * (-a * v.pow(2) + c * u * v - b * u.pow(2)));
    const x2 =
        (d * v.pow(2) + c * m * v - e * u * v - 2 * b * m * u - v * can) /
        (2 * (-b * u.pow(2) + c * u * v - a * v.pow(2)));
    const y2 =
        (e * u.pow(2) + c * m * u - d * u * v - 2 * a * m * v + u * can) /
        (2 * (-a * v.pow(2) + c * u * v - b * u.pow(2)));

    return [point(x1, y1), point(x2, y2)];
}

export function intersectionCircleEllipseLegacy(
    cC: Point,
    cR: number,
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number
): Point[] {
    return intersectionEllipsesLegacy(cC, cR, cR, 0, pC, aEll, bEll, rotateRadEll);
}

export function intersectionEllipsesLegacy(
    pC: Point,
    aEll: number,
    bEll: number,
    rotateRadEll: number,
    pC1: Point,
    aEll1: number,
    bEll1: number,
    rotateRadEll1: number
): Point[] {
    const coefs1 = _calculateImplicitFormulaCoefficients(pC.x, pC.y, aEll, bEll, rotateRadEll);
    const coefs2 = _calculateImplicitFormulaCoefficients(pC1.x, pC1.y, aEll1, bEll1, rotateRadEll1);

    // Define functions for the numerator and denominator for calculating x positions
    const numerator = (y: number): number => c * y ** 2 + e * y + f;
    const denominator = (y: number): number => b * y + d;

    // Get coefficients for implicit equation (IE) representing the intersection of the two ellipses
    // eliminating the x^2 term
    const IE = Coefficients.elliminateTerm(coefs1, coefs2, 'a');
    const { b, c, d, e, f } = IE;

    // Get the implicit coefficients for one of the two ellipses
    const { a: sa, b: sb, c: sc, d: sd, e: se, f: sf } = coefs1;

    // Calculate the coefficients for the quartic function of y
    const p = c * d + b * e;
    const q = d * e + b * f;
    const z4 = sa * c * c + sc * b * b - sb * b * c;
    const z3 = 2 * sa * c * e + 2 * sc * b * d + se * b * b - sd * b * c - sb * p;
    const z2 = sa * (2 * c * f + e * e) + sc * d * d + 2 * se * b * d + sf * b * b - sd * p - sb * q;
    const z1 = 2 * sa * e * f + se * d * d + 2 * sf * b * d - sd * q - sb * d * f;
    const z0 = sa * f * f + sf * d * d - sd * d * f;

    // Get the quartic function of y and solve it for all roots
    const quartic = PolyBase.getPoly([z4, z3, z2, z1, z0]);
    let roots = quartic.roots();

    // Remove duplicates and complex roots using custom EPSILON
    roots = Complex.removeDuplicates(roots, Complex.EPSILON);
    roots = Complex.filterRealRoots(roots, Complex.EPSILON);

    // Calculate the x position for all real roots
    const intersects: Point[] = [];
    for (const r of roots) {
        const d = denominator(r.real);

        // If denominator is near zero, solve a quadratic function in terms of x
        if (Math.abs(d) < Complex.EPSILON) {
            const a = sa;
            const b = sb * r.real + sd;
            const c = sc * r.real ** 2 + se * r.real + sf;
            let qroots = PolyBase.getPoly([a, b, c]).roots();

            qroots = Complex.removeDuplicates(qroots, Complex.EPSILON);
            qroots = Complex.filterRealRoots(qroots, Complex.EPSILON);

            for (const qr of qroots) {
                intersects.push(new Point(qr.real, r.real));
            }
        } else {
            const n = numerator(r.real);
            intersects.push(new Point(-n / d, r.real));
        }
    }

    return intersects;
}

/**
 * Calculate the coefficients for the implicit equation
 *  a.x^2 + b.x.y + c.y^2 + d.x + e.y + f = 0
 * by first rotating the ellipse then translating it.
 */
function _calculateImplicitFormulaCoefficients(
    _cx: number,
    _cy: number,
    _xr: number,
    _yr: number,
    _ang: number
): Coefficients {
    const angle = -_ang;
    const A = Math.cos(angle),
        B = Math.sin(angle);
    const cx = _cx,
        cy = _cy;
    const xr2 = _xr ** 2,
        yr2 = _yr ** 2;
    const AA = (A * A) / xr2 + (B * B) / yr2;
    const BB = (-2 * A * B) / xr2 + (2 * A * B) / yr2;
    const CC = (B * B) / xr2 + (A * A) / yr2;
    const a = AA;
    const b = BB;
    const c = CC;
    const d = -(2 * AA * cx + BB * cy);
    const e = -(BB * cx + 2 * CC * cy);
    const f = AA * cx * cx + BB * cx * cy + CC * cy * cy - 1;
    return new Coefficients(a, b, c, d, e, f);
}
