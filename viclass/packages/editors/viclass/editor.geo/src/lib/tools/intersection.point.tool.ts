import { Point, point } from '@flatten-js/core';
import { <PERSON>rrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncPreviewCommands, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, GeoRenderElement, RenderVertex, StrokeType } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { RepeatSelector } from '../selectors';
import { nLines, SelectedStroke, strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildIntersectionRequest, getElementConstructionDetails } from './util.construction';
import { calculateIntersections, filterVisibleIntersections, getOrderedIntersections } from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    pointsEqual,
    remoteConstruct,
} from './util.tool';

/**
 * Intersection Point Tool - Creates intersection points between two geometric elements
 * Follows standardized geometry tool patterns for selection, preview, and construction
 */
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    declare selLogic: RepeatSelector<SelectedStroke>;
    private pQ = new PreviewQueue();
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];
    private allIntersections: Point[] = []; // All intersections including outside
    private visibleIntersections: Point[] = []; // Only intersections within bounds

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Creates selection logic using standardized selector pattern
     */
    private createSelLogic() {
        this.selLogic = nLines(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.performIntersectionPreview.bind(this),
        });
    }

    override resetState() {
        this.selLogic?.reset();
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    /**
     * Handles selection attempts following standardized pattern
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // Check if user clicked on existing intersection preview
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint && event.eventType === 'pointerup') {
                    this.performConstruction(ctrl, previewPoint);
                    return;
                }
            }
        }

        // Try selection with the selector
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Generates intersection preview when two elements are selected
     * Calculates both all intersections and visible intersections
     */
    private async performIntersectionPreview(selector: RepeatSelector<SelectedStroke>, docCtrl: GeoDocCtrl) {
        const selectedElements = selector.selected || [];
        if (selectedElements.length !== 2) return;

        // Clear previous previews and constructed intersections
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        // Calculate all intersections first
        this.allIntersections = calculateIntersections(stroke1, stroke2, docCtrl) || [];
        // Filter visible intersections based on element bounds
        this.visibleIntersections = filterVisibleIntersections(this.allIntersections, stroke1, stroke2, docCtrl);

        if (this.visibleIntersections.length === 0) {
            this.resetState();
            return;
        }

        // Special case for line-line: construct immediately
        if (isElementLine(stroke1) && isElementLine(stroke2)) {
            if (this.visibleIntersections.length === 0) return;
            const intersectionVertex = pVertex(-9998, [this.visibleIntersections[0].x, this.visibleIntersections[0].y]);
            this.intersectionPreview.push(intersectionVertex);
            await this.performConstruction(docCtrl, intersectionVertex);
            return;
        }

        // Create preview points for other intersection types
        // Only show visible intersections in preview
        this.createIntersectionPreviews(this.visibleIntersections, docCtrl);
    }

    /**
     * Creates preview vertices for intersection points in the correct order
     * Order matches the backend calculation to ensure nth parameter consistency
     */
    private createIntersectionPreviews(intersections: { x: number; y: number }[], docCtrl: GeoDocCtrl) {
        // Create preview vertices in the same order as intersections array
        // This ensures nth parameter calculation matches backend behavior
        intersections.forEach((intersection, index) => {
            const previewVertex = pVertex(-9998 - index, [intersection.x, intersection.y]);
            this.intersectionPreview.push(previewVertex);
            syncPreviewCommands(previewVertex, docCtrl);
        });

        // Enable filtering for multiple intersection points
        if (this.intersectionPreview.length > 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    /**
     * Performs construction of selected intersection point
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const { pcs, points } = await assignNames(
            docCtrl,
            [intersectionSelected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Giao Điểm'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const construction = this.buildConstructionRequest(intersectionSelected, points[0].name, docCtrl);
        if (!construction) return;

        try {
            await remoteConstruct(docCtrl, construction, [], this.editor.geoGateway, 'giao điểm');
            this.intersectionConstructed.push(intersectionSelected);

            // Update remaining preview points
            if (this.intersectionPreview.length - this.intersectionConstructed.length === 0) this.resetState();
            else this.intersectionConstructed.forEach(p => syncRemovePreviewCmd(p, docCtrl));
        } catch (e) {
            this.resetState();
            throw e;
        }
    }

    /**
     * Builds construction request based on selected elements and intersection point
     */
    private buildConstructionRequest(
        intersectionPoint: RenderVertex,
        pointName: string,
        doc: GeoDocCtrl
    ): GeoElConstructionRequest | undefined {
        const selectedElements = this.selLogic.selected;
        if (!selectedElements || selectedElements.length !== 2) return undefined;

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        const paramA = getElementConstructionDetails(stroke1);
        const paramB = getElementConstructionDetails(stroke2);

        // Determine intersection type
        const cgName = this.getIntersectionTypeName(stroke1, stroke2);
        if (!cgName) return undefined;

        // Calculate nth parameter for multiple intersections
        const currentNth = this.calculateNthParameter(intersectionPoint.relIndex, doc);

        return buildIntersectionRequest({
            cgName,
            outputName: pointName,
            paramA,
            paramB,
            nth: cgName === 'LineLine' ? undefined : currentNth,
        });
    }

    /**
     * Gets intersection type name for construction
     */
    private getIntersectionTypeName(element1: StrokeType, element2: StrokeType): string | undefined {
        if (isElementLine(element1) && isElementLine(element2)) return 'LineLine';
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        )
            return 'LineCircle';
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        )
            return 'LineSector';
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        )
            return 'LineEllipse';
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') return 'CircleCircle';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        )
            return 'CircleSector';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        )
            return 'CircleEllipse';
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') return 'SectorSector';
        if (
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector')
        )
            return 'SectorEllipse';
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') return 'EllipseEllipse';
        return undefined;
    }

    /**
     * Calculates nth parameter for intersection point selection
     * Uses the same ordering logic as the backend to ensure consistent nth parameter calculation
     */
    private calculateNthParameter(relIdx: number, doc: GeoDocCtrl): number | undefined {
        if (this.allIntersections.length === 1) return undefined;

        // Find which visible intersection was selected
        const selectedVisibleIndex = this.intersectionPreview.findIndex(p => p.relIndex === relIdx);
        if (selectedVisibleIndex < 0) return undefined;

        const selectedVisiblePoint = point(
            this.visibleIntersections[selectedVisibleIndex].x,
            this.visibleIntersections[selectedVisibleIndex].y
        );
        if (!selectedVisiblePoint) return undefined;

        const selectedElements = this.selLogic.selected || [];
        if (selectedElements.length !== 2) return undefined;

        const stroke1 = strk(selectedElements[0]);
        const stroke2 = strk(selectedElements[1]);
        const orderedIntersections = getOrderedIntersections(stroke1, stroke2, this.allIntersections, doc);
        if (!orderedIntersections) return undefined;

        // Find the position in the ordered intersections array
        const orderedIndex = orderedIntersections.findIndex(pt => pointsEqual(pt, selectedVisiblePoint));

        // Return 0-based index to match backend expectation (backend uses nth directly)
        return orderedIndex >= 0 ? orderedIndex : undefined;
    }
}
