import { Position } from '@viclass/editor.core';
import { GeoElConstructionRequest, ParamSpecs, RenderCircle, RenderEllipse, RenderLine, RenderSector } from '../model';
import { isElementLine } from './util.tool';

export type IntersectionElementDetails = {
    name: string; // Name of the geometric element (e.g., "l1", "c1")
    elType?: string; // Specific type like "LineSegment" used in params if exists
    labelType: string; // Generic type for tpl: "Line", "Circle", "Ellipse", "Sector"
    defId: string; // ParamDefId for this element type: "aLine", "aCircle", etc.
};

export function getElementConstructionDetails(
    el: RenderLine | RenderCircle | RenderEllipse | RenderSector
): IntersectionElementDetails {
    if (isElementLine(el)) {
        const defId = (() => {
            switch (el.elType) {
                case 'Ray':
                    return 'aRay';
                case 'LineSegment':
                    return 'aLineSegment';
                case 'VectorVi':
                    return 'aVector';
                default:
                    return 'aLine';
            }
        })();
        return { name: el.name, elType: el.elType, labelType: 'Line', defId };
    } else if (el.type === 'RenderCircle')
        return { name: el.name, elType: el.elType, labelType: 'Circle', defId: 'aCircle' };
    else if (el.type === 'RenderEllipse')
        return { name: el.name, elType: el.elType, labelType: 'Ellipse', defId: 'anEllipse' };
    else if (el.type === 'RenderSector')
        return { name: el.name, elType: el.elType, labelType: 'Sector', defId: 'aCircularSector' };

    // Should not happen due to filtering
    throw new Error(`Unknown element type for intersection: ${el.type}`);
}

export function buildPointConstruction(name: string, pointPos: Position): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('Point/PointFromCoordsEC', 'Point', 'G2d');
    construction.name = name;
    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-2DPoint',
            params: {
                value: {
                    type: 'array',
                    values: [pointPos.x.toString(), pointPos.y.toString()],
                },
            },
        },
    ];

    return construction;
}

export function buildLineSegmentConstruction(name: string): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('LineSegment/LineSegmentEC', 'LineSegment', 'ByPointsName');
    construction.name = name;
    construction.paramSpecs = [];

    return construction;
}

export function buildIntersectionRequest(args: {
    cgName: string; // From Kotlin CGS enum, e.g., "LineLine"
    outputName: string;
    paramA: IntersectionElementDetails;
    paramB: IntersectionElementDetails;
    nth?: number; // 1-based index if present
}): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'Point/IntersectionPointEC', // Matches Kotlin class name
        'Point', // Output element type
        args.cgName,
        args.outputName
    );

    const paramSpecs: ParamSpecs[] = [
        {
            paramDefId: args.paramA.defId,
            indexInCG: 0,
            optional: false,
            tplStrLangId: `tpl-IntersectionOf${args.paramA.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramA.name },
            },
        },
        {
            paramDefId: args.paramB.defId,
            indexInCG: 1,
            optional: false,
            tplStrLangId: `tpl-IntersectionWith${args.paramB.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramB.name },
            },
        },
    ];

    if (args.nth !== undefined) {
        paramSpecs.push({
            paramDefId: 'aValue', // nth parameter is always 'aValue'
            indexInCG: 2,
            optional: true, // nth is conceptually optional
            tplStrLangId: 'tpl-thIntersection',
            params: { value: { type: 'singleValue', value: args.nth.toString() } },
        });
    }
    construction.paramSpecs = paramSpecs;
    return construction;
}

/**
 * Build angle construction from lines using FromLinesAndDirection method
 * This utility function can be reused across different angle creation tools
 */
export function buildAngleConstructionFromLines(
    lineStartName: string,
    lineStartType: string,
    lineEndName: string,
    lineEndType: string,
    angleName: string,
    anglePointName: string,
    directionStart: number,
    directionEnd: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromLinesAndDirection');
    construction.name = angleName;
    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-LineStart',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-LineEnd',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineEndName,
                },
            },
            dataTypes: {
                name: lineEndType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-IntersectionOfLine',
            params: {
                name: {
                    type: 'singleValue',
                    value: anglePointName,
                },
            },
        },
        {
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-DirectionOfLineStart',
            params: {
                value: {
                    type: 'singleValue',
                    value: directionStart,
                },
            },
        },
        {
            indexInCG: 4,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-DirectionOfLineEnd',
            params: {
                value: {
                    type: 'singleValue',
                    value: directionEnd,
                },
            },
        },
    ];

    return construction;
}

export function buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'anAngle',
            optional: false,
            tplStrLangId: 'tpl-BisectorOfAngle',
            params: {
                name: {
                    type: 'singleValue',
                    value: angleName,
                },
            },
        },
    ];

    return construction;
}

export function buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'anAngle',
            optional: false,
            tplStrLangId: 'tpl-BisectorOfAngle',
            params: {
                name: {
                    type: 'singleValue',
                    value: angleName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-ScalingFactor',
            params: {
                value: {
                    type: 'singleValue',
                    value: k,
                },
            },
        },
    ];

    return construction;
}

export function buildBisectorSegmentAndIntersectionLineConstruction(
    name: string,
    angleName: string,
    intersectionLineName: string,
    intersectionLineType: string
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/BisectorOfAngleEC',
        'LineVi',
        'BisectorAngleSegmentWithIntersectionLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'anAngle',
            optional: false,
            tplStrLangId: 'tpl-BisectorOfAngle',
            params: {
                name: {
                    type: 'singleValue',
                    value: angleName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-IntersectionLine',
            params: {
                name: {
                    type: 'singleValue',
                    value: intersectionLineName,
                },
            },
            dataTypes: {
                name: intersectionLineType,
            },
        },
    ];

    return construction;
}

// Construction methods - made public to implement LineToolConstructionMethods interface
export function buildParallelLine(
    name: string,
    lineStartName: string,
    lineStartType: string,
    throughPointName: string
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointParallelWithLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
    ];

    return construction;
}

export function buildParallelLineSegment(
    name: string,
    lineStartName: string,
    lineStartType: string,
    throughPointName: string,
    k: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointSegmentParallelWithLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-2DPoint',
            params: {
                value: {
                    type: 'singleValue',
                    value: k,
                },
            },
        },
    ];

    return construction;
}

export function buildParallelLineSegmentWithIntersectLine(
    name: string,
    lineStartName: string,
    lineStartType: string,
    intersectionLineName: string,
    intersectionLineType: string,
    throughPointName: string
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointSegmentParallelWithLineAndIntersectionLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-IntersectionLine',
            params: {
                name: {
                    type: 'singleValue',
                    value: intersectionLineName,
                },
            },
            dataTypes: {
                name: intersectionLineType,
            },
        },
    ];

    return construction;
}

// Construction methods remain the same but simplified
export function buildPerpendicularLine(
    name: string,
    lineStartName: string,
    lineStartType: string,
    throughPointName: string
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'ThoughPointPerpendicularWithLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
    ];

    return construction;
}

export function buildPerpendicularLineSegment(
    name: string,
    lineStartName: string,
    lineStartType: string,
    throughPointName: string,
    k: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'PerpendicularWithNewPoint'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: 'tpl-2DPoint',
            params: {
                value: {
                    type: 'singleValue',
                    value: k,
                },
            },
        },
    ];

    return construction;
}

export function buildPerpendicularLineSegmentWithIntersectLine(
    name: string,
    lineStartName: string,
    lineStartType: string,
    intersectionLineName: string,
    intersectionLineType: string,
    throughPointName: string
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'PerpendicularWithIntersectLine'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: lineStartName,
                },
            },
            dataTypes: {
                name: lineStartType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithLine',
            params: {
                name: {
                    type: 'singleValue',
                    value: intersectionLineName,
                },
            },
            dataTypes: {
                name: intersectionLineType,
            },
        },
    ];

    return construction;
}

// Parallel construction functions for curved elements
export function buildThroughPointParallelWithCircle(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    circle: RenderCircle,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointParallelWithCircle'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aCircle',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithCircle',
            params: {
                name: {
                    type: 'singleValue',
                    value: circle.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}

export function buildThroughPointParallelWithEllipse(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    ellipse: RenderEllipse,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointParallelWithEllipse'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'anEllipse',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithEllipse',
            params: {
                name: {
                    type: 'singleValue',
                    value: ellipse.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}

export function buildThroughPointParallelWithSector(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    sector: RenderSector,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/ParallelWithOtherEC',
        'LineVi',
        'ThroughPointParallelWithSector'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-ParallelWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aSector',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithSector',
            params: {
                name: {
                    type: 'singleValue',
                    value: sector.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}

// Perpendicular construction functions for curved elements
export function buildThroughPointPerpendicularWithCircle(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    circle: RenderCircle,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'ThroughPointPerpendicularWithCircle'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aCircle',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithCircle',
            params: {
                name: {
                    type: 'singleValue',
                    value: circle.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}

export function buildThroughPointPerpendicularWithEllipse(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    ellipse: RenderEllipse,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'ThroughPointPerpendicularWithEllipse'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'anEllipse',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithEllipse',
            params: {
                name: {
                    type: 'singleValue',
                    value: ellipse.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}

export function buildThroughPointPerpendicularWithSector(
    name: string,
    throughPointName: string,
    baseLine: RenderLine,
    sector: RenderSector,
    nthIntersection?: number
): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'LineVi/PerpendicularWithOtherEC',
        'LineVi',
        'ThroughPointPerpendicularWithSector'
    );
    construction.name = name;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: 'aPoint',
            optional: false,
            tplStrLangId: 'tpl-ThroughPoint',
            params: {
                name: {
                    type: 'singleValue',
                    value: throughPointName,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aLine',
            optional: false,
            tplStrLangId: 'tpl-PerpendicularWith',
            params: {
                name: {
                    type: 'singleValue',
                    value: baseLine.name,
                },
            },
            dataTypes: {
                name: baseLine.elType,
            },
        },
        {
            indexInCG: 2,
            paramDefId: 'aSector',
            optional: false,
            tplStrLangId: 'tpl-IntersectionWithSector',
            params: {
                name: {
                    type: 'singleValue',
                    value: sector.name,
                },
            },
        },
    ];

    // Add optional nth intersection parameter if specified
    if (nthIntersection !== undefined) {
        construction.paramSpecs.push({
            indexInCG: 3,
            paramDefId: 'aValue',
            optional: true,
            tplStrLangId: 'tpl-thIntersection',
            params: {
                value: {
                    type: 'singleValue',
                    value: nthIntersection,
                },
            },
        });
    }

    return construction;
}
