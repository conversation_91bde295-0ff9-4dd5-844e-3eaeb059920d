import { Point, point } from '@flatten-js/core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoEpsilon, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, strk, stroke, then, ThenSelector, vert, vertex, VertexOnStroke, vertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    calculateIntersections,
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    createCircleFromSector,
    filterVisibleIntersections,
    getOrderedIntersections,
    intersectionLineEllipse,
} from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementCurve,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<CommonToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    directionVector: number[] | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.directionVector = undefined;
        super.resetState();
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): GeoElConstructionRequest;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): GeoElConstructionRequest;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract buildCurvedElementConstruction(
        combinedName: string,
        baseLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        nthIntersection?: number
    ): GeoElConstructionRequest;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract getSimpleConstructionLabel(): string;

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract getComplexConstructionLabel(): string;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line -> Point -> Preview -> Final Point Selection
     */
    protected createSelLogic() {
        // First selector: select a line
        const lineSelector = stroke({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: el => isElementLine(el),
        });

        // Second selector: select a point to define through which the line passes
        const firstPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Third selector: enhanced vertex selector with projection for final point on line
        const finalVertexSelector = or(
            [
                // Option 1: Select free vertex with projection onto line
                vertex({
                    preview: true, // Allow selecting preview elements (including first point if it was a preview)
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) =>
                        this.projectOnLine(this.selectedPoint, previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToIntersection(
                            this.selectedLine,
                            this.selectedPoint,
                            this.previewLine,
                            stroke,
                            previewVertex,
                            doc
                        ),
                    cfunc: (stroke, doc) => this.checkStrokeIntersection(this.previewLine, stroke, doc),
                    refinedFilter: el => isElementLine(el) || isElementCurve(el),
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: line -> point -> final vertex
        this.selLogic = then([lineSelector, firstPointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [line, throughPoint, finalVertexSelection] = selector.selected;

                this.selectedLine = strk(line as RenderLine) as RenderLine;
                this.selectedPoint = vert(throughPoint as RenderVertex);
                const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                // Check if final vertex is the same as the through point
                if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                    // If vertices are the same, use simple line construction
                    if (!this.previewLine) {
                        console.error('Preview line not available for same point construction');
                        this.resetState();
                        return;
                    }
                    await this.handleSimpleLineConstruction(
                        doc,
                        this.selectedLine,
                        this.selectedPoint,
                        this.previewLine
                    );
                } else {
                    const finalVertexStroke = strk(finalVertexSelection as RenderLine);
                    await this.handleComplexLineConstruction(
                        doc,
                        this.selectedLine,
                        this.selectedPoint,
                        this.previewLine,
                        finalVertex,
                        isElementLine(finalVertexStroke) || isElementCurve(finalVertexStroke)
                            ? finalVertexStroke
                            : undefined
                    );
                }
            },
        });
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * Enhanced to support curved elements
     */
    protected checkStrokeIntersection(
        previewLine: RenderLine,
        finalVertexStroke: StrokeType,
        doc: GeoDocCtrl
    ): boolean {
        if (isElementLine(finalVertexStroke)) return this.checkLineIntersection(finalVertexStroke as RenderLine, doc);

        if (isElementCurve(finalVertexStroke))
            return this.checkCurvedElementIntersection(
                previewLine,
                finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                doc
            );

        return false;
    }

    /**
     * Transform function to project any point onto the line preview
     */
    protected projectOnLine(throughPoint: RenderVertex, previewEl: RenderVertex, _doc: GeoDocCtrl): RenderVertex {
        // Check if the preview point is very close to the through point (same point selection)
        const distance = Math.hypot(
            previewEl.coords[0] - throughPoint.coords[0],
            previewEl.coords[1] - throughPoint.coords[1]
        );

        // If user clicks on the same point as through point, keep it there
        if (distance < GeoEpsilon) {
            previewEl.coords[0] = throughPoint.coords[0];
            previewEl.coords[1] = throughPoint.coords[1];
            if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
            return previewEl;
        }

        const projectedCoords = projectPointOntoLine(previewEl.coords, throughPoint.coords, this.directionVector);

        if (projectedCoords) {
            previewEl.coords[0] = projectedCoords[0];
            previewEl.coords[1] = projectedCoords[1];
            if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
        }

        return previewEl;
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     * Enhanced to support curved elements
     */
    protected projectVertexOnStrokeToIntersection(
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine,
        stroke: StrokeType,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        // Don't project if it's the same as preview line
        if (isElementLine(stroke) && previewLine.relIndex === (stroke as RenderLine).relIndex) {
            return previewVertex;
        }

        return this.projectFinalVertex(baseLine, throughPoint, previewLine, previewVertex, stroke, doc);
    }

    /**
     * Check if vertex can be projected onto preview line
     */
    protected canProjectVertexOntoPreviewLine(vertex: RenderVertex): boolean {
        if (!this.selectedPoint || !this.directionVector) return false;

        // Always allow projection for free vertices
        return vertex.relIndex !== this.selectedPoint.relIndex;
    }

    /**
     * Check if line intersects with preview line
     */
    protected checkLineIntersection(line: RenderLine, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false;

        try {
            const intersections = calculateLineLineIntersection(this.previewLine, line, doc);
            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking line intersection:', error);
            return false;
        }
    }

    /**
     * Check if curved element intersects with preview line
     */
    protected checkCurvedElementIntersection(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        doc: GeoDocCtrl
    ): boolean {
        if (!this.previewLine) return false;

        try {
            switch (curvedElement.type) {
                case 'RenderCircle': {
                    const circleIntersections = calculateLineCircleIntersection(previewLine, curvedElement, doc);
                    return circleIntersections && circleIntersections.length > 0;
                }

                case 'RenderEllipse': {
                    const ellipseIntersections = intersectionLineEllipse(previewLine, curvedElement, doc);
                    return ellipseIntersections && ellipseIntersections.length > 0;
                }

                case 'RenderSector': {
                    const circleIntersections =
                        calculateLineCircleIntersection(previewLine, createCircleFromSector(curvedElement), doc) || [];
                    const sectorIntersections = filterVisibleIntersections(
                        circleIntersections,
                        previewLine,
                        curvedElement,
                        doc
                    );
                    return sectorIntersections && sectorIntersections.length > 0;
                }

                default:
                    return false;
            }
        } catch (error) {
            console.warn('Error checking curved element intersection:', error);
            return false;
        }
    }

    /**
     * Project final vertex onto preview line or find intersection with curved elements
     */
    protected projectFinalVertex(
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine,
        finalVertex: RenderVertex,
        stroke: StrokeType,
        doc: GeoDocCtrl
    ): RenderVertex {
        try {
            // If no stroke, project onto preview line
            if (!stroke) {
                return this.projectVertexOntoPreviewLine(finalVertex);
            }

            // If stroke is a line, find intersection
            if (isElementLine(stroke)) {
                return this.projectFinalVertexOnLine(finalVertex, stroke as RenderLine, doc);
            }

            // If stroke is a curved element, find intersection
            if (isElementCurve(stroke)) {
                return this.projectFinalVertexOnCurvedElement(
                    previewLine,
                    finalVertex,
                    stroke as RenderCircle | RenderEllipse | RenderSector,
                    doc
                );
            }

            return finalVertex;
        } catch (error) {
            console.warn('Error projecting final vertex:', error);
            return finalVertex;
        }
    }

    /**
     * Project vertex onto preview line
     */
    protected projectVertexOntoPreviewLine(vertex: RenderVertex): RenderVertex {
        if (!this.selectedPoint || !this.directionVector) return vertex;

        try {
            const projectedCoords = projectPointOntoLine(
                vertex.coords,
                this.selectedPoint.coords,
                this.directionVector
            );
            if (projectedCoords) {
                vertex.coords[0] = projectedCoords[0];
                vertex.coords[1] = projectedCoords[1];
                if (vertex.coords.length > 2) vertex.coords[2] = 0;
            }
            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex onto preview line:', error);
            return vertex;
        }
    }

    /**
     * Project final vertex on line stroke to intersection point
     */
    protected projectFinalVertexOnLine(vertex: RenderVertex, line: RenderLine, doc: GeoDocCtrl): RenderVertex {
        if (!this.previewLine) return vertex;

        try {
            const intersections = calculateLineLineIntersection(this.previewLine, line, doc);
            if (intersections && intersections.length > 0) {
                // For line-line intersection, there's typically only one intersection
                // Use first intersection (no ordering needed for single point)
                const intersection = intersections[0];
                vertex.coords[0] = intersection.x;
                vertex.coords[1] = intersection.y;
                if (vertex.coords.length > 2) vertex.coords[2] = 0;
            }
            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex on line:', error);
            return vertex;
        }
    }

    /**
     * Project final vertex on curved element to intersection point
     */
    protected projectFinalVertexOnCurvedElement(
        previewLine: RenderLine,
        vertex: RenderVertex,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        doc: GeoDocCtrl
    ): RenderVertex {
        if (!this.previewLine) return vertex;

        try {
            let intersections: Point[] = [];

            switch (curvedElement.type) {
                case 'RenderCircle':
                    intersections = calculateLineCircleIntersection(previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderEllipse':
                    intersections = intersectionLineEllipse(previewLine, curvedElement, doc) || [];
                    break;

                case 'RenderSector':
                    const tempCircle = createCircleFromSector(curvedElement);
                    const circleIntersections = calculateLineCircleIntersection(previewLine, tempCircle, doc) || [];
                    intersections = filterVisibleIntersections(circleIntersections, previewLine, curvedElement, doc);
                    break;
            }

            if (intersections.length > 0) {
                // For projection, use closest intersection to current vertex position
                const selectedIntersection = this.selectIntersectionForProjection(vertex, intersections);

                if (selectedIntersection) {
                    vertex.coords[0] = selectedIntersection.x;
                    vertex.coords[1] = selectedIntersection.y;
                    if (vertex.coords.length > 2) vertex.coords[2] = 0;
                }
            }

            return vertex;
        } catch (error) {
            console.warn('Error projecting vertex on curved element:', error);
            return vertex;
        }
    }

    /**
     * Find the closest intersection point to the given vertex
     * This is used for projection operations where we want the most natural/closest intersection
     */
    protected findClosestIntersection(vertex: RenderVertex, intersections: any[]): any {
        if (intersections.length === 1) return intersections[0];

        let closestIntersection = intersections[0];
        let minDistance = Math.hypot(intersections[0].x - vertex.coords[0], intersections[0].y - vertex.coords[1]);

        for (let i = 1; i < intersections.length; i++) {
            const distance = Math.hypot(intersections[i].x - vertex.coords[0], intersections[i].y - vertex.coords[1]);
            if (distance < minDistance) {
                minDistance = distance;
                closestIntersection = intersections[i];
            }
        }

        return closestIntersection;
    }

    /**
     * Select intersection for projection operations (real-time cursor movement)
     * Uses closest intersection for natural user experience - vertex snaps to nearest intersection
     * This is different from construction which uses ordered intersections for consistency
     */
    protected selectIntersectionForProjection(vertex: RenderVertex, intersections: any[]): any {
        if (!intersections || intersections.length === 0) return null;
        // For projection, always use closest intersection to provide natural UX
        return this.findClosestIntersection(vertex, intersections);
    }

    /**
     * Calculate the nth intersection index based on the final vertex position
     * This determines which intersection the user actually selected through projection
     */
    protected calculateNthIntersectionFromVertex(
        previewLine: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        finalVertex: RenderVertex,
        doc: GeoDocCtrl
    ): number {
        const allIntersections = calculateIntersections(previewLine, curvedElement, doc) || [];
        if (allIntersections.length === 0) throw new Error('Không có giao điểm');
        if (allIntersections.length === 1) return 0;

        const orderedIntersections = getOrderedIntersections(previewLine, curvedElement, allIntersections, doc);

        // Find which ordered intersection is closest to the final vertex position
        let closestIndex = 0;
        let minDistance = Math.hypot(
            orderedIntersections[0].x - finalVertex.coords[0],
            orderedIntersections[0].y - finalVertex.coords[1]
        );

        for (let i = 1; i < orderedIntersections.length; i++) {
            const distance = Math.hypot(
                orderedIntersections[i].x - finalVertex.coords[0],
                orderedIntersections[i].y - finalVertex.coords[1]
            );
            if (distance < minDistance) {
                minDistance = distance;
                closestIndex = i;
            }
        }

        return closestIndex;
    }

    /**
     * Handle curved element construction with enhanced logic
     * Uses consistent intersection ordering and nth intersection selection
     */
    protected handleCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        nthIntersection?: number
    ): any {
        // This method can be overridden by subclasses for specific curved element handling
        // Default implementation uses the abstract buildCurvedElementConstruction method
        const combinedName = `${throughPoint.name}${finalVertex.name}`;

        // For construction, use ordered intersection selection
        // If nth intersection is not specified, use first ordered intersection (not closest)
        const actualNthIntersection = nthIntersection !== undefined ? nthIntersection : 0;

        return this.buildCurvedElementConstruction(
            combinedName,
            line,
            curvedElement,
            throughPoint,
            actualNthIntersection
        );
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(
        ctrl: GeoDocCtrl,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine
    ) {
        try {
            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                previewLine
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(previewLine.name, baseLine, throughPoint);

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        ctrl: GeoDocCtrl,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        previewLine: RenderLine,
        finalVertex: RenderVertex,
        finalVertexStroke?: RenderLine | RenderCircle | RenderEllipse | RenderSector
    ) {
        try {
            // Project final vertex to appropriate position
            if (finalVertexStroke)
                finalVertex = this.projectFinalVertex(
                    baseLine,
                    throughPoint,
                    previewLine,
                    finalVertex,
                    finalVertexStroke,
                    ctrl
                );

            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;
            if (isElementLine(finalVertexStroke)) {
                // Use intersection construction
                const construction = this.buildLineSegmentWithIntersectionConstruction(
                    combinedName,
                    baseLine,
                    finalVertexStroke as RenderLine,
                    throughPoint
                );
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else if (isElementCurve(finalVertexStroke)) {
                // Calculate which intersection the user actually selected based on finalVertex position
                const nthIntersection = this.calculateNthIntersectionFromVertex(
                    baseLine,
                    finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                    finalVertex,
                    ctrl
                );

                // Use curved element construction with the correct nth intersection
                const construction = this.handleCurvedElementConstruction(
                    ctrl,
                    baseLine,
                    finalVertexStroke as RenderCircle | RenderEllipse | RenderSector,
                    throughPoint,
                    finalVertex,
                    nthIntersection // Use the calculated intersection index
                );

                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else {
                // Use segment construction with scaling factor
                const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                let k = 0;
                if (startPt && endPt && this.directionVector) {
                    // Calculate vector from through point to final vertex
                    const toEndVector = [
                        finalVertex.coords[0] - throughPoint.coords[0],
                        finalVertex.coords[1] - throughPoint.coords[1],
                    ];

                    // Calculate dot product to determine direction
                    const dotProduct =
                        toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                    // Distance from through point to final vertex
                    const distance = startPt.distanceTo(endPt)[0];

                    // k is positive if same direction, negative if opposite direction
                    k = dotProduct >= 0 ? distance : -distance;
                }

                const construction = this.buildLineSegmentConstruction(combinedName, baseLine, throughPoint, k);
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private get baseLine(): RenderLine {
        return this.selLogic.selected?.[0] as RenderLine;
    }

    private get throughPoint(): RenderVertex {
        return this.selLogic.selected?.[1] as RenderVertex;
    }

    protected doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        if (this.baseLine && this.throughPoint) this.createLinePreview(ctrl, this.selectedLine, this.selectedPoint);
        else 

        this.pQ.flush(ctrl);
    }
}
