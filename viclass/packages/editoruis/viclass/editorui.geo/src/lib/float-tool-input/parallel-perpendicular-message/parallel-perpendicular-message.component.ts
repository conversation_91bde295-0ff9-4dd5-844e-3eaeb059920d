import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { GeometryToolBar, GeometryToolType } from '@viclass/editor.geo';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for displaying current selection message for both CreateParallelLineTool and CreatePerpendicularLineTool
 * Shows step-by-step instructions based on current selection state
 */
@Component({
    selector: 'tb-parallel-perpendicular-message',
    templateUrl: './parallel-perpendicular-message.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParallelPerpendicularMessageComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;
    private _currentMessage: string = '';
    private pollingInterval: any;
    private lastLoggedState: string = '';

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
        // Initialize with initial message
        this._currentMessage = this.getInitialMessage();
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
            this.updateInputFromToolState();
        }, 0);

        // Start polling to check for tool state changes
        // Since parallel/perpendicular tools don't have specific tool state events,
        // we need to poll for changes in their internal state
        this.pollingInterval = setInterval(() => {
            this.updateInputFromToolState();
        }, 200); // Check every 200ms (reduced frequency)
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
    }

    get displayMessage(): string {
        return this._currentMessage;
    }

    get currentMessage(): string {
        const tool = this.toolbar.getTool(this.tooltype);

        if (!tool) {
            return this.getInitialMessage();
        }

        // Access the tool's internal state to determine current step
        const baseParallelPerpendicularTool = tool as any;

        // Check if we have selected line and point
        const hasSelectedLine = baseParallelPerpendicularTool.selectedLine;
        const hasSelectedPoint = baseParallelPerpendicularTool.selectedPoint;
        const hasPreviewLine = baseParallelPerpendicularTool.previewLine;

        // Create state string for comparison to avoid unnecessary updates
        const currentState = `${!!hasSelectedLine}-${!!hasSelectedPoint}-${!!hasPreviewLine}`;
        this.lastLoggedState = currentState;

        // Check selLogic state to determine current step more accurately
        const selLogic = baseParallelPerpendicularTool.selLogic;
        let currentStep = 1; // Default to step 1

        if (selLogic && selLogic.selected) {
            if (Array.isArray(selLogic.selected)) {
                const selectedCount = selLogic.selected.length;

                if (selectedCount === 1) {
                    // Only line selected, waiting for point
                    currentStep = 2;
                } else if (selectedCount === 2) {
                    // Line and point selected, waiting for final vertex
                    currentStep = 3;
                } else if (selectedCount >= 3) {
                    // All selections complete
                    currentStep = 4;
                }
            } else {
                // Single selection (not array yet)
                currentStep = 2;
            }
        }

        if (currentStep === 1) {
            // Step 1: Select base line
            return this.getSelectLineMessage();
        } else if (currentStep === 2) {
            // Step 2: Select through point
            return this.getSelectPointMessage();
        } else if (currentStep >= 3) {
            // Step 3: Select final position or intersection
            return this.getSelectFinalPositionMessage();
        }

        return this.getInitialMessage();
    }

    private getInitialMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Bắt đầu tạo đường thẳng song song';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Bắt đầu tạo đường thẳng vuông góc';
        }
        return '';
    }

    private getSelectLineMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn đường thẳng gốc để tạo đường song song';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn đường thẳng gốc để tạo đường vuông góc';
        }
        return '';
    }

    private getSelectPointMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn điểm mà đường song song sẽ đi qua';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn điểm mà đường vuông góc sẽ đi qua';
        }
        return '';
    }

    private getSelectFinalPositionMessage(): string {
        if (this.tooltype === 'CreateParallelLineTool') {
            return 'Chọn vị trí cuối hoặc giao điểm với một đường để hoàn thành đường song song';
        } else if (this.tooltype === 'CreatePerpendicularLineTool') {
            return 'Chọn vị trí cuối hoặc giao điểm với một đường để hoàn thành đường vuông góc';
        }
        return '';
    }

    updateInputFromToolState() {
        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
            const newMessage = this.currentMessage;
            if (this._currentMessage !== newMessage) {
                this._currentMessage = newMessage;
                this.changeDetectorRef.detectChanges();
            }
        }, 0);
    }
}
